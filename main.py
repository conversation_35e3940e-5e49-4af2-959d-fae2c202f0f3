#!/usr/bin/env python3
"""
CBRE AI Validation Agent - Main Entry Point

This is the main entry point for the AI-powered validation agent system.
It provides both command-line interface and programmatic access to the
comprehensive validation chain.
"""

import sys
import argparse
import json
import logging
from pathlib import Path
from typing import Dict, Any

from agent_chain import run_validation_chain
from utils.logging_config import setup_logging
from utils.openai_client import get_openai_client

def setup_argument_parser() -> argparse.ArgumentParser:
    """Setup command line argument parser."""

    parser = argparse.ArgumentParser(
        description="CBRE AI Validation Agent - Comprehensive data validation using AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py procedure.sql data.xlsx
  python main.py --sp procedure.sql --data data.csv --nav-processing
  python main.py --sp procedure.sql --data data.xlsx --batch-size 200 --output results.json
        """
    )

    # Required arguments
    parser.add_argument(
        "stored_procedure",
        nargs="?",
        help="Path to the stored procedure SQL file"
    )
    parser.add_argument(
        "data_file",
        nargs="?",
        help="Path to the data file (Excel/CSV) to validate"
    )

    # Optional arguments
    parser.add_argument(
        "--sp", "--stored-procedure",
        dest="stored_procedure_alt",
        help="Alternative way to specify stored procedure file"
    )
    parser.add_argument(
        "--data", "--data-file",
        dest="data_file_alt",
        help="Alternative way to specify data file"
    )
    parser.add_argument(
        "--nav-processing",
        action="store_true",
        default=True,
        help="Enable NAV-specific file processing (default: enabled)"
    )
    parser.add_argument(
        "--no-nav-processing",
        action="store_true",
        help="Disable NAV-specific file processing"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=100,
        help="Batch size for processing large files (default: 100)"
    )
    parser.add_argument(
        "--output", "-o",
        help="Output file for results (JSON format)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress non-error output"
    )
    parser.add_argument(
        "--config",
        default="config/settings.yaml",
        help="Path to configuration file (default: config/settings.yaml)"
    )

    return parser

def validate_arguments(args) -> Dict[str, Any]:
    """Validate and normalize command line arguments."""

    # Determine file paths
    sp_path = args.stored_procedure or args.stored_procedure_alt
    data_path = args.data_file or args.data_file_alt

    # Use defaults if not provided
    if not sp_path:
        sp_path = "examples/FundHoldings.sql"
        print(f"No stored procedure specified, using default: {sp_path}")

    if not data_path:
        data_path = "examples/FundHoldings.xlsx"
        print(f"No data file specified, using default: {data_path}")

    # Validate file existence
    if not Path(sp_path).exists():
        raise FileNotFoundError(f"Stored procedure file not found: {sp_path}")

    if not Path(data_path).exists():
        raise FileNotFoundError(f"Data file not found: {data_path}")

    # Determine NAV processing setting
    enable_nav_processing = args.nav_processing and not args.no_nav_processing

    return {
        "sp_path": sp_path,
        "data_path": data_path,
        "enable_nav_processing": enable_nav_processing,
        "batch_size": args.batch_size,
        "output_file": args.output,
        "verbose": args.verbose,
        "quiet": args.quiet,
        "config_path": args.config
    }

def setup_logging_level(verbose: bool, quiet: bool):
    """Setup appropriate logging level based on arguments."""

    if quiet:
        logging.getLogger().setLevel(logging.ERROR)
    elif verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)

def print_results_summary(results: Dict[str, Any], quiet: bool = False):
    """Print a summary of validation results."""

    if quiet:
        return

    print("\n" + "="*60)
    print("VALIDATION RESULTS SUMMARY")
    print("="*60)

    # Basic metrics
    metrics = results.get("metrics", {})
    print(f"Status: {results.get('status', 'Unknown').upper()}")
    print(f"Processing Time: {metrics.get('total_processing_time', 0):.2f} seconds")
    print(f"Rows Processed: {metrics.get('total_rows_processed', 0):,}")
    print(f"Rules Extracted: {metrics.get('rules_extracted', 0)}")
    print(f"Error Rate: {metrics.get('overall_error_rate', 0):.1%}")

    # Violations
    violations = results.get("violations", [])
    if violations:
        print(f"\nViolations Detected: {len(violations)}")

        # Group by severity
        severity_counts = {}
        for violation in violations:
            severity = violation.get("severity", "unknown")
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        for severity, count in severity_counts.items():
            print(f"  {severity.capitalize()}: {count}")
    else:
        print("\n✅ No violations detected!")

    # Output file
    if results.get("file"):
        print(f"\nOutput File: {results['file']}")

    # NAV processing report
    if "nav_processing_report" in results:
        nav_report = results["nav_processing_report"]
        print(f"\nNAV Processing:")
        print(f"  Original Rows: {nav_report.get('original_rows', 0):,}")
        print(f"  Processed Rows: {nav_report.get('processed_rows', 0):,}")
        print(f"  Cleanup Actions: {len(nav_report.get('cleanup_actions', []))}")

        validation_results = nav_report.get('validation_results', {})
        print(f"  Validation: {'PASSED' if validation_results.get('passed', False) else 'FAILED'}")

def main():
    """Main entry point for the validation agent."""

    try:
        # Parse command line arguments
        parser = setup_argument_parser()
        args = parser.parse_args()

        # Validate and normalize arguments
        config = validate_arguments(args)

        # Setup logging
        setup_logging(config["config_path"])
        setup_logging_level(config["verbose"], config["quiet"])

        logger = logging.getLogger(__name__)

        if not config["quiet"]:
            print("🔍 CBRE AI Validation Agent")
            print("="*40)
            print(f"Stored Procedure: {config['sp_path']}")
            print(f"Data File: {config['data_path']}")
            print(f"NAV Processing: {'Enabled' if config['enable_nav_processing'] else 'Disabled'}")
            print(f"Batch Size: {config['batch_size']}")
            print()

        # Initialize OpenAI client (will check for API key)
        try:
            openai_client = get_openai_client()
            logger.info("OpenAI client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            print("❌ Error: OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.")
            return 1

        # Run the validation chain
        logger.info("Starting validation chain")

        results = run_validation_chain(
            config["sp_path"],
            config["data_path"],
            enable_nav_processing=config["enable_nav_processing"],
            batch_size=config["batch_size"]
        )

        # Print results summary
        print_results_summary(results, config["quiet"])

        # Save results to file if specified
        if config["output_file"]:
            try:
                with open(config["output_file"], 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                print(f"\n📄 Results saved to: {config['output_file']}")
            except Exception as e:
                logger.error(f"Failed to save results to file: {e}")
                print(f"❌ Error saving results: {e}")

        # Return appropriate exit code
        status = results.get("status", "unknown")
        if status == "blocked":
            logger.warning("Validation chain was blocked due to violations")
            return 2
        elif status == "review_required":
            logger.info("Validation chain completed but requires review")
            return 1
        else:
            logger.info("Validation chain completed successfully")
            return 0

    except KeyboardInterrupt:
        print("\n❌ Validation interrupted by user")
        return 130

    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        return 1

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Validation failed with error: {e}", exc_info=True)
        print(f"❌ Validation failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)