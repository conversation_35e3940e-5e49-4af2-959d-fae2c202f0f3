import pandas as pd
from pathlib import Path
from typing import Optional

def load_sql_file(path: str) -> str:
    """Load SQL file with robust encoding detection and fallback."""
    path_obj = Path(path)

    # Try multiple encodings in order of preference
    encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1252', 'iso-8859-1', 'latin1']

    for encoding in encodings_to_try:
        try:
            return path_obj.read_text(encoding=encoding)
        except UnicodeDecodeError:
            continue
        except Exception as e:
            # If it's not a unicode error, re-raise it
            raise e

    # If all encodings fail, try with error handling
    try:
        return path_obj.read_text(encoding='utf-8', errors='replace')
    except Exception as e:
        raise RuntimeError(f"Could not read file {path} with any encoding: {e}")

def read_text_file_robust(file_path: str, encodings: Optional[list] = None) -> str:
    """
    Robust text file reader that tries multiple encodings.

    Args:
        file_path: Path to the text file
        encodings: List of encodings to try (optional)

    Returns:
        File content as string

    Raises:
        RuntimeError: If file cannot be read with any encoding
    """
    if encodings is None:
        encodings = ['utf-8', 'utf-8-sig', 'cp1252', 'iso-8859-1', 'latin1']

    path_obj = Path(file_path)

    for encoding in encodings:
        try:
            return path_obj.read_text(encoding=encoding)
        except UnicodeDecodeError:
            continue
        except Exception as e:
            raise e

    # Final attempt with error replacement
    try:
        return path_obj.read_text(encoding='utf-8', errors='replace')
    except Exception as e:
        raise RuntimeError(f"Could not read file {file_path} with any encoding: {e}")

def load_excel_as_dicts(path: str) -> list:
    df = pd.read_excel(path)
    return df.to_dict(orient="records")

def save_output_excel(rows: list, procedure_name: str) -> str:
    df = pd.DataFrame(rows)
    out_path = f"data/output/{procedure_name}_validated.xlsx"
    Path("data/output").mkdir(parents=True, exist_ok=True)
    df.to_excel(out_path, index=False)
    return out_path