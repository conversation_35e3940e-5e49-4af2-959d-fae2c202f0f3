import pandas as pd
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import numpy as np
from datetime import datetime

logger = logging.getLogger(__name__)

class NAVFileProcessor:
    """
    Specialized processor for NAV (Net Asset Value) files with comprehensive
    cleanup rules and validation requirements as specified in user requirements.
    """
    
    def __init__(self):
        self.cleanup_rules = {
            'strip_special_chars': True,
            'remove_total_rows': True,
            'extract_from_parentheses': True
        }
        
        self.validation_rules = {
            'reject_empty_files': True,
            'check_unique_ids': True,
            'validate_financial_fields': True
        }
        
        self.required_columns = ['UNIQUE_ID', 'FUND_ID', 'NAV']
        self.financial_columns = ['NAV', 'PREVIOUS_NAV', 'AMOUNT', 'VALUE']
        
    def process_nav_file(self, file_path: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Process NAV file with comprehensive cleanup and validation.
        
        Args:
            file_path: Path to the NAV file
            
        Returns:
            Tuple of (processed_dataframe, processing_report)
        """
        logger.info(f"Starting NAV file processing: {file_path}")
        
        processing_report = {
            'file_path': file_path,
            'original_rows': 0,
            'processed_rows': 0,
            'cleanup_actions': [],
            'validation_results': {},
            'errors': [],
            'warnings': []
        }
        
        try:
            # Load the file
            df = self._load_file(file_path)
            processing_report['original_rows'] = len(df)
            
            logger.info(f"Loaded NAV file with {len(df)} rows and {len(df.columns)} columns")
            
            # Phase 1: Data Cleanup
            df, cleanup_actions = self._apply_cleanup_rules(df)
            processing_report['cleanup_actions'] = cleanup_actions
            
            # Phase 2: Data Validation
            validation_results = self._apply_validation_rules(df)
            processing_report['validation_results'] = validation_results
            
            # Phase 3: Final processing
            df = self._finalize_processing(df)
            processing_report['processed_rows'] = len(df)
            
            logger.info(f"NAV file processing completed: {processing_report['processed_rows']} rows processed")
            
            return df, processing_report
            
        except Exception as e:
            error_msg = f"NAV file processing failed: {str(e)}"
            logger.error(error_msg)
            processing_report['errors'].append(error_msg)
            raise
    
    def _load_file(self, file_path: str) -> pd.DataFrame:
        """Load NAV file with appropriate format detection."""
        
        file_path = Path(file_path)
        
        if file_path.suffix.lower() == '.csv':
            df = pd.read_csv(file_path)
        elif file_path.suffix.lower() in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")
        
        # Basic file validation
        if df.empty:
            raise ValueError("File is empty")
        
        return df
    
    def _apply_cleanup_rules(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """Apply comprehensive cleanup rules to NAV data."""
        
        cleanup_actions = []
        df_cleaned = df.copy()
        
        # Rule 1: Strip special characters
        if self.cleanup_rules['strip_special_chars']:
            original_shape = df_cleaned.shape
            df_cleaned = self._strip_special_characters(df_cleaned)
            cleanup_actions.append(f"Stripped special characters from all text fields")
        
        # Rule 2: Remove TOTAL rows
        if self.cleanup_rules['remove_total_rows']:
            original_rows = len(df_cleaned)
            df_cleaned = self._remove_total_rows(df_cleaned)
            removed_rows = original_rows - len(df_cleaned)
            if removed_rows > 0:
                cleanup_actions.append(f"Removed {removed_rows} TOTAL/summary rows")
        
        # Rule 3: Extract values from parentheses
        if self.cleanup_rules['extract_from_parentheses']:
            df_cleaned = self._extract_from_parentheses(df_cleaned)
            cleanup_actions.append("Extracted values from parenthetical expressions")
        
        # Additional NAV-specific cleanup
        df_cleaned = self._nav_specific_cleanup(df_cleaned)
        cleanup_actions.append("Applied NAV-specific data cleanup")
        
        logger.info(f"Cleanup completed: {len(cleanup_actions)} actions performed")
        return df_cleaned, cleanup_actions
    
    def _strip_special_characters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Strip special characters from text fields."""
        
        df_cleaned = df.copy()
        
        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'object':
                # Remove common special characters but preserve meaningful ones
                df_cleaned[col] = df_cleaned[col].astype(str).str.replace(
                    r'[^\w\s\.\-\(\)\/]', '', regex=True
                ).str.strip()
        
        return df_cleaned
    
    def _remove_total_rows(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove rows that contain totals or summary information."""
        
        # Identify TOTAL rows by common patterns
        total_patterns = [
            r'(?i)total',
            r'(?i)sum',
            r'(?i)subtotal',
            r'(?i)grand total',
            r'(?i)summary'
        ]
        
        # Check all text columns for total patterns
        mask = pd.Series([False] * len(df))
        
        for col in df.columns:
            if df[col].dtype == 'object':
                for pattern in total_patterns:
                    mask |= df[col].astype(str).str.contains(pattern, na=False, regex=True)
        
        # Remove rows matching total patterns
        df_cleaned = df[~mask].copy()
        
        return df_cleaned
    
    def _extract_from_parentheses(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract values from parenthetical expressions."""
        
        df_cleaned = df.copy()
        
        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'object':
                # Extract content from parentheses and use as the main value
                df_cleaned[col] = df_cleaned[col].astype(str).apply(
                    lambda x: self._extract_parenthetical_value(x)
                )
        
        return df_cleaned
    
    def _extract_parenthetical_value(self, value: str) -> str:
        """Extract value from parentheses if present."""
        
        if pd.isna(value) or value == 'nan':
            return value
        
        # Look for content in parentheses
        parentheses_match = re.search(r'\(([^)]+)\)', str(value))
        
        if parentheses_match:
            extracted = parentheses_match.group(1).strip()
            # If extracted value looks more meaningful, use it
            if len(extracted) > 0 and not extracted.isspace():
                return extracted
        
        return str(value)
    
    def _nav_specific_cleanup(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply NAV-specific cleanup rules."""
        
        df_cleaned = df.copy()
        
        # Standardize column names
        column_mapping = {
            'unique_id': 'UNIQUE_ID',
            'uniqueid': 'UNIQUE_ID',
            'fund_id': 'FUND_ID',
            'fundid': 'FUND_ID',
            'nav': 'NAV',
            'net_asset_value': 'NAV',
            'fund_code': 'FUND_CODE',
            'fundcode': 'FUND_CODE'
        }
        
        # Apply column name standardization
        for old_name, new_name in column_mapping.items():
            if old_name in df_cleaned.columns:
                df_cleaned = df_cleaned.rename(columns={old_name: new_name})
        
        # Clean financial columns
        for col in self.financial_columns:
            if col in df_cleaned.columns:
                df_cleaned[col] = self._clean_financial_column(df_cleaned[col])
        
        # Clean date columns
        date_columns = ['VALUATION_DATE', 'DATE', 'REPORTING_DATE']
        for col in date_columns:
            if col in df_cleaned.columns:
                df_cleaned[col] = self._clean_date_column(df_cleaned[col])
        
        return df_cleaned
    
    def _clean_financial_column(self, series: pd.Series) -> pd.Series:
        """Clean financial data columns."""
        
        cleaned = series.copy()
        
        # Remove currency symbols and formatting
        if cleaned.dtype == 'object':
            cleaned = cleaned.astype(str).str.replace(r'[\$,£€¥]', '', regex=True)
            cleaned = cleaned.str.replace(r'[^\d\.\-]', '', regex=True)
            
            # Convert to numeric
            cleaned = pd.to_numeric(cleaned, errors='coerce')
        
        return cleaned
    
    def _clean_date_column(self, series: pd.Series) -> pd.Series:
        """Clean date columns."""
        
        cleaned = series.copy()
        
        # Attempt to parse dates
        if cleaned.dtype == 'object':
            cleaned = pd.to_datetime(cleaned, errors='coerce')
        
        return cleaned
    
    def _apply_validation_rules(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Apply comprehensive validation rules to NAV data."""
        
        validation_results = {
            'passed': True,
            'errors': [],
            'warnings': [],
            'metrics': {}
        }
        
        # Rule 1: Reject empty files
        if self.validation_rules['reject_empty_files']:
            if df.empty:
                validation_results['errors'].append("File is empty after cleanup")
                validation_results['passed'] = False
        
        # Rule 2: Check unique IDs
        if self.validation_rules['check_unique_ids']:
            unique_id_validation = self._validate_unique_ids(df)
            validation_results['metrics']['unique_ids'] = unique_id_validation
            
            if not unique_id_validation['valid']:
                validation_results['errors'].extend(unique_id_validation['errors'])
                validation_results['passed'] = False
        
        # Rule 3: Validate financial fields
        if self.validation_rules['validate_financial_fields']:
            financial_validation = self._validate_financial_fields(df)
            validation_results['metrics']['financial_fields'] = financial_validation
            
            if not financial_validation['valid']:
                validation_results['errors'].extend(financial_validation['errors'])
                validation_results['passed'] = False
        
        # Additional NAV-specific validations
        nav_validation = self._validate_nav_specific_rules(df)
        validation_results['metrics']['nav_specific'] = nav_validation
        
        if nav_validation['warnings']:
            validation_results['warnings'].extend(nav_validation['warnings'])
        
        logger.info(f"Validation completed: {'PASSED' if validation_results['passed'] else 'FAILED'}")
        return validation_results
    
    def _validate_unique_ids(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate UNIQUE_ID requirements."""
        
        result = {
            'valid': True,
            'errors': [],
            'metrics': {}
        }
        
        if 'UNIQUE_ID' not in df.columns:
            result['errors'].append("UNIQUE_ID column is missing")
            result['valid'] = False
            return result
        
        # Check for missing UNIQUE_IDs
        missing_ids = df['UNIQUE_ID'].isna().sum()
        if missing_ids > 0:
            result['errors'].append(f"{missing_ids} rows have missing UNIQUE_ID")
            result['valid'] = False
        
        # Check for duplicate UNIQUE_IDs
        duplicate_ids = df['UNIQUE_ID'].duplicated().sum()
        if duplicate_ids > 0:
            result['errors'].append(f"{duplicate_ids} duplicate UNIQUE_IDs found")
            result['valid'] = False
        
        result['metrics'] = {
            'total_ids': len(df),
            'missing_ids': missing_ids,
            'duplicate_ids': duplicate_ids,
            'unique_ids': df['UNIQUE_ID'].nunique()
        }
        
        return result
    
    def _validate_financial_fields(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate that financial fields are not all null."""
        
        result = {
            'valid': True,
            'errors': [],
            'metrics': {}
        }
        
        financial_cols_present = [col for col in self.financial_columns if col in df.columns]
        
        if not financial_cols_present:
            result['errors'].append("No financial columns found in data")
            result['valid'] = False
            return result
        
        # Check each financial column
        for col in financial_cols_present:
            null_count = df[col].isna().sum()
            total_count = len(df)
            null_percentage = (null_count / total_count) * 100 if total_count > 0 else 100
            
            result['metrics'][col] = {
                'null_count': null_count,
                'null_percentage': null_percentage
            }
            
            # If all values are null, it's an error
            if null_count == total_count:
                result['errors'].append(f"All values in {col} are null")
                result['valid'] = False
        
        return result
    
    def _validate_nav_specific_rules(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Apply NAV-specific validation rules."""
        
        result = {
            'warnings': [],
            'metrics': {}
        }
        
        # Check NAV value ranges
        if 'NAV' in df.columns:
            nav_stats = self._analyze_nav_values(df['NAV'])
            result['metrics']['nav_analysis'] = nav_stats
            
            # Warning for unusual NAV values
            if nav_stats['negative_count'] > 0:
                result['warnings'].append(f"{nav_stats['negative_count']} negative NAV values found")
            
            if nav_stats['zero_count'] > 0:
                result['warnings'].append(f"{nav_stats['zero_count']} zero NAV values found")
            
            if nav_stats['max_value'] > 1_000_000_000:  # $1B threshold
                result['warnings'].append(f"Very high NAV values detected (max: ${nav_stats['max_value']:,.2f})")
        
        return result
    
    def _analyze_nav_values(self, nav_series: pd.Series) -> Dict[str, Any]:
        """Analyze NAV values for patterns and anomalies."""
        
        # Convert to numeric if needed
        nav_numeric = pd.to_numeric(nav_series, errors='coerce')
        
        return {
            'count': len(nav_numeric),
            'null_count': nav_numeric.isna().sum(),
            'negative_count': (nav_numeric < 0).sum(),
            'zero_count': (nav_numeric == 0).sum(),
            'min_value': nav_numeric.min(),
            'max_value': nav_numeric.max(),
            'mean_value': nav_numeric.mean(),
            'median_value': nav_numeric.median()
        }
    
    def _finalize_processing(self, df: pd.DataFrame) -> pd.DataFrame:
        """Final processing steps."""
        
        # Ensure required columns are present
        for col in self.required_columns:
            if col not in df.columns:
                logger.warning(f"Required column {col} not found in processed data")
        
        # Sort by UNIQUE_ID if present
        if 'UNIQUE_ID' in df.columns:
            df = df.sort_values('UNIQUE_ID')
        
        # Reset index
        df = df.reset_index(drop=True)
        
        return df

# Global processor instance
_nav_processor = None

def get_nav_processor() -> NAVFileProcessor:
    """Get or create the global NAV file processor instance."""
    global _nav_processor
    if _nav_processor is None:
        _nav_processor = NAVFileProcessor()
    return _nav_processor
