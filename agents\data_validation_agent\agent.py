import logging
import time
import traceback
from typing import Dict, Any, Optional, List
from .prompt_builder import build_data_validation_prompt
from .schema import BusinessValidationResult
from utils.openai_client import get_openai_client
from utils.json_schema_utils import validate_json_structure
from agents.guardrail_agent.log_utils import get_agent_logger

logger = logging.getLogger(__name__)

def validate_business_logic(row: dict, rules_json: dict, reference_data: dict,
                          history: dict, row_index: Optional[int] = None) -> BusinessValidationResult:
    """
    AI-powered business logic validation agent that performs sophisticated
    business rule validation with contextual analysis.

    Args:
        row: The data row to validate
        rules_json: The extracted validation rules
        reference_data: Reference data for lookups
        history: Historical data for trend analysis
        row_index: Optional row index for logging

    Returns:
        BusinessValidationResult with detailed validation outcome

    Raises:
        RuntimeError: If validation fails due to system errors
        ValueError: If response validation fails
    """
    agent_logger = get_agent_logger()
    openai_client = get_openai_client()

    start_time = time.time()

    # Prepare activity details for logging
    activity_details = {
        "row_index": row_index,
        "rules_count": len(rules_json.get("rules", [])),
        "reference_data_size": len(reference_data) if reference_data else 0,
        "history_data_size": len(history) if history else 0,
        "processing_time_ms": 0,
        "input_size": len(str(row)) + len(str(rules_json)) + len(str(reference_data)) + len(str(history))
    }

    try:
        logger.debug(f"Starting business logic validation for row {row_index or 'unknown'}")

        # Enhance reference data and history with additional context
        enhanced_reference_data = _enhance_reference_data(reference_data, row)
        enhanced_history = _enhance_historical_context(history, row)

        # Build the validation prompt
        try:
            prompt = build_data_validation_prompt(row, rules_json, enhanced_reference_data, enhanced_history)
        except Exception as e:
            error_msg = f"Failed to build business validation prompt: {e}"
            logger.error(error_msg)
            agent_logger.log_error("data_validation_agent", "prompt_building_error", error_msg,
                                 {"row_index": row_index, "row_data": row}, traceback.format_exc())
            raise RuntimeError(error_msg)

        # Call OpenAI API with enhanced error handling
        try:
            system_message = (
                "You are an expert business analyst and data validation specialist. "
                "Analyze the provided data against business rules, considering historical patterns, "
                "reference data integrity, and complex business logic. Provide detailed reasoning "
                "for your decisions and assess confidence levels."
            )

            response = openai_client.call_openai(
                prompt=prompt,
                system_message=system_message,
                agent_name="data_validation_agent",
                temperature=0.1  # Slightly higher for nuanced business decisions
            )

            activity_details["output_size"] = len(response)

        except Exception as e:
            error_msg = f"OpenAI API call failed for business validation: {e}"
            logger.error(error_msg)
            agent_logger.log_error("data_validation_agent", "api_call_error", error_msg,
                                 {"row_index": row_index}, traceback.format_exc())
            raise RuntimeError(error_msg)

        # Validate and parse the JSON response
        try:
            validation_result = validate_json_structure(response, "business_validation_schema")

            if not validation_result["parsed"]:
                error_msg = f"Failed to parse JSON response: {validation_result['errors']}"
                logger.error(error_msg)
                agent_logger.log_error("data_validation_agent", "json_parsing_error", error_msg,
                                     {"row_index": row_index, "response_preview": response[:500]})
                raise ValueError(error_msg)

            if not validation_result["valid"]:
                logger.warning(f"Response validation failed: {validation_result['errors']}")
                # Continue processing but log the warning
                agent_logger.log_error("data_validation_agent", "schema_validation_warning",
                                     f"Response schema validation failed: {validation_result['errors']}",
                                     {"row_index": row_index})

            # Parse into Pydantic model
            result = BusinessValidationResult.model_validate_json(response)

            # Additional business logic validation and enhancement
            _enhance_business_validation_result(result, row, rules_json, enhanced_reference_data,
                                              enhanced_history, agent_logger, row_index)

            # Calculate processing time
            processing_time = time.time() - start_time
            activity_details["processing_time_ms"] = int(processing_time * 1000)
            activity_details["validation_result"] = "invalid" if not result.valid else "valid"
            activity_details["confidence_score"] = getattr(result, 'confidence_score', None)

            # Log successful completion
            agent_logger.log_agent_activity(
                "data_validation_agent",
                "validate_business_logic",
                activity_details,
                "success"
            )

            logger.debug(f"Business validation completed for row {row_index or 'unknown'} "
                        f"in {processing_time:.3f}s - Result: {'INVALID' if not result.valid else 'VALID'}")

            return result

        except Exception as e:
            error_msg = f"Failed to validate or parse response: {e}"
            logger.error(error_msg)
            agent_logger.log_error("data_validation_agent", "response_validation_error", error_msg,
                                 {"row_index": row_index, "response": response[:1000]},
                                 traceback.format_exc())
            raise ValueError(error_msg)

    except Exception as e:
        # Calculate processing time even for failures
        processing_time = time.time() - start_time
        activity_details["processing_time_ms"] = int(processing_time * 1000)

        # Log failed activity
        agent_logger.log_agent_activity(
            "data_validation_agent",
            "validate_business_logic",
            activity_details,
            "error"
        )

        logger.error(f"Business validation failed for row {row_index or 'unknown'}: {e}")
        raise

def _enhance_reference_data(reference_data: dict, row: dict) -> dict:
    """
    Enhance reference data with additional context relevant to the current row.

    Args:
        reference_data: Original reference data
        row: Current data row

    Returns:
        Enhanced reference data with additional context
    """
    enhanced = reference_data.copy() if reference_data else {}

    # Add row-specific context
    enhanced["current_row_context"] = {
        "fund_id": row.get("FUND_ID"),
        "currency": row.get("CURRENCY"),
        "valuation_date": row.get("VALUATION_DATE")
    }

    # Add validation metadata
    enhanced["validation_metadata"] = {
        "reference_data_available": bool(reference_data),
        "critical_fields_present": all(field in row for field in ["UNIQUE_ID", "FUND_ID", "NAV"])
    }

    return enhanced

def _enhance_historical_context(history: dict, row: dict) -> dict:
    """
    Enhance historical context with trend analysis and patterns.

    Args:
        history: Original historical data
        row: Current data row

    Returns:
        Enhanced historical context with trend analysis
    """
    enhanced = history.copy() if history else {}

    # Add trend analysis context
    enhanced["trend_analysis"] = {
        "historical_data_available": bool(history),
        "fund_id": row.get("FUND_ID"),
        "current_nav": row.get("NAV")
    }

    # Calculate basic trends if historical data is available
    if history and "previous_nav_values" in history:
        try:
            current_nav = float(row.get("NAV", 0))
            previous_values = [float(v) for v in history["previous_nav_values"] if v is not None]

            if previous_values:
                latest_previous = previous_values[-1]
                if latest_previous > 0:
                    percentage_change = ((current_nav - latest_previous) / latest_previous) * 100
                    enhanced["trend_analysis"]["percentage_change"] = percentage_change
                    enhanced["trend_analysis"]["trend_direction"] = "increasing" if percentage_change > 0 else "decreasing"
        except (ValueError, TypeError, ZeroDivisionError):
            enhanced["trend_analysis"]["calculation_error"] = "Unable to calculate trends"

    return enhanced

def _enhance_business_validation_result(result: BusinessValidationResult, row: dict, rules_json: dict,
                                      reference_data: dict, history: dict, agent_logger,
                                      row_index: Optional[int]) -> None:
    """
    Enhance business validation result with additional analysis and warnings.

    Args:
        result: The validation result to enhance
        row: The original data row
        rules_json: The validation rules
        reference_data: Reference data used
        history: Historical data used
        agent_logger: Logger instance
        row_index: Row index for context
    """
    warnings = []

    # Check confidence score if available
    confidence_score = getattr(result, 'confidence_score', None)
    if confidence_score is not None and confidence_score < 0.7:
        warnings.append(f"Low confidence score ({confidence_score:.2f}) - consider manual review")

    # Check for data completeness issues
    if not reference_data or len(reference_data) == 0:
        warnings.append("No reference data available for validation")

    if not history or len(history) == 0:
        warnings.append("No historical data available for trend analysis")

    # Check for unusual business patterns
    if "NAV" in row:
        try:
            nav_value = float(row["NAV"]) if row["NAV"] is not None else None
            if nav_value is not None:
                # Check for extreme values
                if nav_value > 10_000_000_000:  # $10B threshold for business validation
                    warnings.append(f"Extremely high NAV value: ${nav_value:,.2f} - verify accuracy")
                elif nav_value == 0:
                    warnings.append("Zero NAV value - verify if this is expected for fund status")
        except (ValueError, TypeError):
            warnings.append("NAV value validation could not be performed - non-numeric value")

    # Log warnings if any
    if warnings:
        agent_logger.log_error("data_validation_agent", "business_validation_warning",
                             f"Business validation warnings for row {row_index}",
                             {"row_index": row_index, "warnings": warnings, "confidence_score": confidence_score})
