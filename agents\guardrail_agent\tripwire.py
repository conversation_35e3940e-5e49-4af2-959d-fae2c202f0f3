import re
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TripwireDetector:
    """
    Advanced tripwire detection system for AI validation agents.
    Implements comprehensive safety checks and anomaly detection.
    """

    def __init__(self):
        self.security_patterns = [
            r'(?i)(drop|delete|truncate|exec|execute)\s+',
            r'(?i)(union|select|insert|update)\s+.*\s+(from|into)',
            r'(?i)(script|javascript|vbscript)',
            r'(?i)(<script|<iframe|<object)',
        ]

        self.suspicious_keywords = [
            'password', 'secret', 'token', 'key', 'credential',
            'admin', 'root', 'system', 'config'
        ]

    def detect_tripwires(self, agent_output: dict, column_template: dict,
                        context: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Comprehensive tripwire detection with detailed violation reporting.

        Args:
            agent_output: Output from AI agent
            column_template: Expected column schema
            context: Additional context for validation

        Returns:
            List of violation dictionaries with details
        """
        violations = []

        # Basic structure validation
        violations.extend(self._check_basic_structure(agent_output))

        # Column schema validation
        violations.extend(self._check_column_schema(agent_output, column_template))

        # Security pattern detection
        violations.extend(self._check_security_patterns(agent_output))

        # Data volume and anomaly detection
        violations.extend(self._check_data_anomalies(agent_output, context))

        # Business logic consistency
        violations.extend(self._check_business_logic(agent_output))

        # Performance and resource checks
        violations.extend(self._check_performance_limits(agent_output, context))

        logger.info(f"Tripwire detection completed: {len(violations)} violations found")
        return violations

    def _check_basic_structure(self, agent_output: dict) -> List[Dict[str, Any]]:
        """Check basic output structure and completeness."""
        violations = []

        # Empty output check
        if not agent_output:
            violations.append({
                "policy": "GP-007",
                "severity": "high",
                "description": "Agent output is empty or null",
                "recommendation": "Investigate agent failure, check prompts and API connectivity"
            })
            return violations

        # Missing critical fields
        if "rules" in agent_output and not agent_output.get("rules"):
            violations.append({
                "policy": "GP-007",
                "severity": "medium",
                "description": "Agent output contains no rules - potential processing failure",
                "recommendation": "Review stored procedure content and parsing logic"
            })

        # Malformed JSON structure
        if isinstance(agent_output, dict):
            for key, value in agent_output.items():
                if key in ['rules', 'violations'] and not isinstance(value, list):
                    violations.append({
                        "policy": "GP-007",
                        "severity": "medium",
                        "description": f"Field '{key}' should be a list but is {type(value).__name__}",
                        "recommendation": "Check agent prompt templates and output formatting"
                    })

        return violations

    def _check_column_schema(self, agent_output: dict, column_template: dict) -> List[Dict[str, Any]]:
        """Check for hallucinated or invalid columns."""
        violations = []
        allowed_columns = set(column_template.get("columns", []))

        if "rules" in agent_output:
            for i, rule in enumerate(agent_output["rules"]):
                rule_columns = rule.get("columns", [])
                for col in rule_columns:
                    if col not in allowed_columns and allowed_columns:  # Only check if we have a schema
                        violations.append({
                            "policy": "GP-002",
                            "severity": "high",
                            "description": f"Rule {i+1}: Column '{col}' not found in file schema",
                            "recommendation": f"Verify column exists in uploaded file or update schema"
                        })

        return violations

    def _check_security_patterns(self, agent_output: dict) -> List[Dict[str, Any]]:
        """Detect potential security threats in agent output."""
        violations = []

        # Convert output to string for pattern matching
        output_str = str(agent_output).lower()

        # Check for SQL injection patterns
        for pattern in self.security_patterns:
            if re.search(pattern, output_str):
                violations.append({
                    "policy": "GP-005",
                    "severity": "critical",
                    "description": f"Potential SQL injection pattern detected: {pattern}",
                    "recommendation": "Block processing immediately, review input sanitization"
                })

        # Check for suspicious keywords
        for keyword in self.suspicious_keywords:
            if keyword in output_str:
                violations.append({
                    "policy": "GP-005",
                    "severity": "medium",
                    "description": f"Suspicious keyword detected: {keyword}",
                    "recommendation": "Review content for potential security implications"
                })

        return violations

    def _check_data_anomalies(self, agent_output: dict, context: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """Detect data volume and value anomalies."""
        violations = []

        # Check rule count anomalies
        if "rules" in agent_output:
            rule_count = len(agent_output["rules"])

            if rule_count > 50:
                violations.append({
                    "policy": "GP-001",
                    "severity": "medium",
                    "description": f"Unusually high number of rules extracted: {rule_count}",
                    "recommendation": "Review stored procedure complexity and parsing logic"
                })

            if rule_count == 0:
                violations.append({
                    "policy": "GP-004",
                    "severity": "high",
                    "description": "No validation rules extracted from stored procedure",
                    "recommendation": "Verify stored procedure contains validation logic"
                })

        # Check for financial value anomalies in validation results
        if "validated_sample" in agent_output:
            self._check_financial_anomalies(agent_output["validated_sample"], violations)

        return violations

    def _check_financial_anomalies(self, sample_data: List[Dict], violations: List[Dict]) -> None:
        """Check for unrealistic financial values."""
        for i, row in enumerate(sample_data):
            # Check NAV values
            nav_value = row.get("NAV")
            if nav_value is not None:
                try:
                    nav_float = float(nav_value)
                    if nav_float > 1_000_000_000:  # $1B threshold
                        violations.append({
                            "policy": "GP-003",
                            "severity": "medium",
                            "description": f"Row {i+1}: NAV value ${nav_float:,.2f} exceeds $1B threshold",
                            "recommendation": "Verify data accuracy, consider if this is expected"
                        })
                    elif nav_float < 0:
                        violations.append({
                            "policy": "GP-003",
                            "severity": "medium",
                            "description": f"Row {i+1}: Negative NAV value ${nav_float:,.2f}",
                            "recommendation": "Review fund type and data accuracy"
                        })
                except (ValueError, TypeError):
                    pass  # Non-numeric values handled elsewhere

    def _check_business_logic(self, agent_output: dict) -> List[Dict[str, Any]]:
        """Check for logical inconsistencies in business rules."""
        violations = []

        if "rules" not in agent_output:
            return violations

        rules = agent_output["rules"]

        # Check for contradictory rules on same columns
        column_rules = {}
        for rule in rules:
            for col in rule.get("columns", []):
                if col not in column_rules:
                    column_rules[col] = []
                column_rules[col].append(rule)

        for col, col_rules in column_rules.items():
            if len(col_rules) > 1:
                # Check for contradictory constraints
                greater_than_rules = [r for r in col_rules if r.get("rule_type") == "greater_than"]
                less_than_rules = [r for r in col_rules if r.get("rule_type") == "less_than"]

                if greater_than_rules and less_than_rules:
                    for gt_rule in greater_than_rules:
                        for lt_rule in less_than_rules:
                            gt_val = gt_rule.get("value", 0)
                            lt_val = lt_rule.get("value", 0)
                            if gt_val >= lt_val:
                                violations.append({
                                    "policy": "GP-002",
                                    "severity": "high",
                                    "description": f"Contradictory rules for {col}: must be > {gt_val} AND < {lt_val}",
                                    "recommendation": "Review stored procedure logic for consistency"
                                })

        return violations

    def _check_performance_limits(self, agent_output: dict, context: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """Check for performance and resource limit violations."""
        violations = []

        if context:
            # Check processing time
            processing_time = context.get("processing_time_seconds", 0)
            if processing_time > 300:  # 5 minutes
                violations.append({
                    "policy": "GP-006",
                    "severity": "medium",
                    "description": f"Processing time {processing_time}s exceeds 5-minute threshold",
                    "recommendation": "Optimize processing logic or increase timeout limits"
                })

            # Check file size
            file_size_mb = context.get("file_size_mb", 0)
            if file_size_mb > 50:
                violations.append({
                    "policy": "GP-001",
                    "severity": "medium",
                    "description": f"File size {file_size_mb}MB exceeds 50MB threshold",
                    "recommendation": "Consider file splitting or streaming processing"
                })

        return violations

# Global detector instance
_detector_instance = None

def get_tripwire_detector() -> TripwireDetector:
    """Get or create the global tripwire detector instance."""
    global _detector_instance
    if _detector_instance is None:
        _detector_instance = TripwireDetector()
    return _detector_instance

# Backward compatibility function
def detect_tripwires(agent_output: dict, column_template: dict) -> list:
    """
    Backward compatibility function for existing code.

    Args:
        agent_output: Output from AI agent
        column_template: Expected column schema

    Returns:
        List of violation messages (simplified format)
    """
    detector = get_tripwire_detector()
    violations = detector.detect_tripwires(agent_output, column_template)

    # Convert to simple string list for backward compatibility
    return [v["description"] for v in violations]