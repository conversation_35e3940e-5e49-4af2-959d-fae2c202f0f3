import logging
import time
from pathlib import Path
from typing import Dict, Any, List, Optional

from agents.sp_parser_agent.agent import parse_stored_procedure
from agents.file_validation_agent.agent import validate_row, validate_batch
from agents.data_validation_agent.agent import validate_business_logic
from agents.guardrail_agent.agent import enforce_guardrails
from utils.file_handler import load_sql_file, load_excel_as_dicts, save_output_excel
from utils.json_schema_utils import validate_rules_schema
from utils.nav_file_processor import get_nav_processor
from utils.logging_config import get_error_reporter, log_agent_activity
from memory.session_context import session_context

logger = logging.getLogger(__name__)

def run_validation_chain(sql_file_path: str, excel_path: str,
                        enable_nav_processing: bool = True,
                        batch_size: int = 100) -> Dict[str, Any]:
    """
    Enhanced validation chain with comprehensive NAV file processing,
    detailed logging, and improved error handling.

    Args:
        sql_file_path: Path to the SQL stored procedure file
        excel_path: Path to the Excel/CSV data file
        enable_nav_processing: Whether to apply NAV-specific processing
        batch_size: Batch size for processing large files

    Returns:
        Comprehensive validation results with detailed metrics
    """

    start_time = time.time()
    error_reporter = get_error_reporter()

    # Initialize processing context
    procedure_name = Path(sql_file_path).stem
    session_context.set("procedure_name", procedure_name)
    session_context.set("start_time", start_time)

    processing_context = {
        "sql_file_path": sql_file_path,
        "excel_path": excel_path,
        "procedure_name": procedure_name,
        "enable_nav_processing": enable_nav_processing,
        "batch_size": batch_size
    }

    logger.info(f"Starting validation chain for procedure: {procedure_name}")

    try:
        # Step 1: Load and parse stored procedure
        logger.info("Step 1: Loading and parsing stored procedure")
        step_start = time.time()

        try:
            sql_text = load_sql_file(sql_file_path)
            rule_result = parse_stored_procedure(sql_text, procedure_name)
            rules_json = rule_result.model_dump()

            step_time = time.time() - step_start
            log_agent_activity("agent_chain", "parse_stored_procedure", {
                "procedure_name": procedure_name,
                "rules_extracted": len(rules_json.get("rules", [])),
                "processing_time_ms": int(step_time * 1000)
            }, "success")

        except Exception as e:
            error_reporter.report_validation_step_error(
                "parse_stored_procedure", sql_text[:500] if 'sql_text' in locals() else None,
                e, processing_context
            )
            raise

        # Step 2: Validate rules structure
        logger.info("Step 2: Validating rules structure")
        try:
            validate_rules_schema(rules_json)
            logger.info(f"Rules schema validation passed: {len(rules_json.get('rules', []))} rules")
        except Exception as e:
            error_reporter.report_validation_step_error(
                "validate_rules_schema", rules_json, e, processing_context
            )
            raise

        # Step 3: Load and process data file
        logger.info("Step 3: Loading and processing data file")
        step_start = time.time()

        try:
            # Apply NAV-specific processing if enabled
            if enable_nav_processing:
                nav_processor = get_nav_processor()
                processed_df, nav_report = nav_processor.process_nav_file(excel_path)

                # Convert to list of dictionaries
                rows = processed_df.to_dict(orient="records")

                processing_context["nav_processing_report"] = nav_report
                logger.info(f"NAV processing completed: {len(rows)} rows processed")

                # Log NAV processing results
                log_agent_activity("agent_chain", "nav_file_processing", {
                    "original_rows": nav_report.get("original_rows", 0),
                    "processed_rows": nav_report.get("processed_rows", 0),
                    "cleanup_actions": len(nav_report.get("cleanup_actions", [])),
                    "validation_passed": nav_report.get("validation_results", {}).get("passed", False)
                }, "success")

            else:
                # Standard file loading
                rows = load_excel_as_dicts(excel_path)
                logger.info(f"Standard file loading completed: {len(rows)} rows loaded")

            step_time = time.time() - step_start
            processing_context["data_loading_time"] = step_time

        except Exception as e:
            error_reporter.report_validation_step_error(
                "load_data_file", excel_path, e, processing_context
            )
            raise

        if not rows:
            raise ValueError("No data rows found in the input file")

        # Step 4: Validate data rows
        logger.info(f"Step 4: Validating {len(rows)} data rows")
        step_start = time.time()

        try:
            # Use batch processing for better performance and logging
            if len(rows) > batch_size:
                logger.info(f"Using batch processing with batch size: {batch_size}")
                validation_results = validate_batch(rows, rules_json, batch_size)
            else:
                # Process individually for smaller datasets
                validation_results = []
                for i, row in enumerate(rows):
                    val_res = validate_row(row, rules_json, i)
                    validation_results.append(val_res)

            step_time = time.time() - step_start
            processing_context["file_validation_time"] = step_time

            # Calculate validation metrics
            error_count = sum(1 for res in validation_results if res.error)
            error_rate = error_count / len(validation_results) if validation_results else 0

            log_agent_activity("agent_chain", "file_validation", {
                "total_rows": len(rows),
                "error_count": error_count,
                "error_rate": error_rate,
                "processing_time_ms": int(step_time * 1000)
            }, "success")

        except Exception as e:
            error_reporter.report_validation_step_error(
                "validate_data_rows", {"row_count": len(rows)}, e, processing_context
            )
            raise

        # Step 5: Business logic validation
        logger.info("Step 5: Performing business logic validation")
        step_start = time.time()

        try:
            business_results = []
            reference_data = {}  # Could be enhanced with actual reference data
            history = {}  # Could be enhanced with historical data

            for i, row in enumerate(rows):
                biz_res = validate_business_logic(row, rules_json, reference_data, history, i)
                business_results.append(biz_res)

            step_time = time.time() - step_start
            processing_context["business_validation_time"] = step_time

            # Calculate business validation metrics
            invalid_count = sum(1 for res in business_results if not res.valid)
            invalid_rate = invalid_count / len(business_results) if business_results else 0

            log_agent_activity("agent_chain", "business_validation", {
                "total_rows": len(rows),
                "invalid_count": invalid_count,
                "invalid_rate": invalid_rate,
                "processing_time_ms": int(step_time * 1000)
            }, "success")

        except Exception as e:
            error_reporter.report_validation_step_error(
                "business_logic_validation", {"row_count": len(rows)}, e, processing_context
            )
            raise

        # Step 6: Combine validation results
        logger.info("Step 6: Combining validation results")

        validated_rows = []
        for i, row in enumerate(rows):
            val_res = validation_results[i] if i < len(validation_results) else None
            biz_res = business_results[i] if i < len(business_results) else None

            # Combine results
            combined_row = row.copy()
            combined_row["validation_error"] = val_res.error if val_res else False
            combined_row["business_invalid"] = not biz_res.valid if biz_res else False
            combined_row["overall_error"] = (val_res.error if val_res else False) or (not biz_res.valid if biz_res else False)

            # Combine error messages
            error_messages = []
            if val_res and val_res.error:
                error_messages.append(val_res.error_message)
            if biz_res and not biz_res.valid:
                error_messages.append(biz_res.reason)

            combined_row["error_message"] = "; ".join(error_messages) if error_messages else ""
            validated_rows.append(combined_row)

        # Step 7: Enforce guardrails
        logger.info("Step 7: Enforcing guardrails")
        step_start = time.time()

        try:
            final_output = {
                "procedure_name": procedure_name,
                "rules": rules_json.get("rules", []),
                "validated_sample": validated_rows[:10],  # Larger sample for better analysis
                "total_rows": len(validated_rows),
                "error_count": sum(1 for row in validated_rows if row.get("overall_error", False)),
                "processing_context": processing_context
            }

            column_template = {"columns": list(rows[0].keys()) if rows else []}
            guardrail_context = {
                "processing_time_seconds": time.time() - start_time,
                "file_size_mb": Path(excel_path).stat().st_size / (1024 * 1024),
                "total_rows": len(validated_rows)
            }

            guardrail_result = enforce_guardrails(final_output, column_template, rules_json, guardrail_context)

            step_time = time.time() - step_start
            processing_context["guardrail_time"] = step_time

        except Exception as e:
            error_reporter.report_validation_step_error(
                "enforce_guardrails", final_output, e, processing_context
            )
            raise

        # Step 8: Save output
        logger.info("Step 8: Saving validation results")

        try:
            output_path = save_output_excel(validated_rows, procedure_name)
            logger.info(f"Validation results saved to: {output_path}")
        except Exception as e:
            logger.warning(f"Failed to save output file: {e}")
            output_path = None

        # Calculate final metrics
        total_time = time.time() - start_time
        overall_error_count = sum(1 for row in validated_rows if row.get("overall_error", False))
        overall_error_rate = overall_error_count / len(validated_rows) if validated_rows else 0

        # Final result
        result = {
            "status": guardrail_result.status,
            "violations": guardrail_result.violations,
            "file": output_path,
            "metrics": {
                "total_processing_time": total_time,
                "total_rows_processed": len(validated_rows),
                "overall_error_count": overall_error_count,
                "overall_error_rate": overall_error_rate,
                "rules_extracted": len(rules_json.get("rules", [])),
                "guardrail_risk_score": getattr(guardrail_result, 'risk_score', None)
            },
            "processing_context": processing_context
        }

        # Add NAV processing report if available
        if enable_nav_processing and "nav_processing_report" in processing_context:
            result["nav_processing_report"] = processing_context["nav_processing_report"]

        # Log final results
        log_agent_activity("agent_chain", "validation_chain_complete", {
            "total_time_seconds": total_time,
            "status": guardrail_result.status,
            "total_violations": len(guardrail_result.violations),
            "error_rate": overall_error_rate,
            "rows_processed": len(validated_rows)
        }, "success")

        logger.info(f"Validation chain completed successfully in {total_time:.2f}s - "
                   f"Status: {guardrail_result.status}, Errors: {overall_error_count}/{len(validated_rows)}")

        return result

    except Exception as e:
        total_time = time.time() - start_time

        # Log the failure
        log_agent_activity("agent_chain", "validation_chain_failed", {
            "total_time_seconds": total_time,
            "error_message": str(e),
            "processing_context": processing_context
        }, "error")

        logger.error(f"Validation chain failed after {total_time:.2f}s: {e}")
        raise
