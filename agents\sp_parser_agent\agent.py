import json
import logging
import time
from typing import Optional, Dict, Any
from .prompt_builder import build_sp_parser_prompt
from .schema import RuleExtractionOutput
from utils.openai_client import get_openai_client
from utils.json_schema_utils import validate_rules_schema, validate_json_structure
from agents.guardrail_agent.log_utils import get_agent_logger
from sqlglot import parse_one, ParseError
import traceback

logger = logging.getLogger(__name__)

def parse_stored_procedure(sql_text: str, procedure_name: str) -> RuleExtractionOutput:
    """
    AI-powered stored procedure parser that extracts validation rules
    with comprehensive error handling and logging.

    Args:
        sql_text: The SQL stored procedure content
        procedure_name: Name of the procedure for context

    Returns:
        RuleExtractionOutput with extracted rules

    Raises:
        RuntimeError: If parsing fails after retries
        ValueError: If output validation fails
    """
    agent_logger = get_agent_logger()
    openai_client = get_openai_client()

    start_time = time.time()

    # Log the start of parsing activity
    activity_details = {
        "procedure_name": procedure_name,
        "sql_length": len(sql_text),
        "processing_time_ms": 0,
        "input_size": len(sql_text),
        "sqlglot_success": False
    }

    try:
        logger.info(f"Starting SP parsing for procedure: {procedure_name}")

        # Attempt structural parse with SQLGlot for validation
        sqlglot_success = False
        try:
            _ = parse_one(sql_text)
            sqlglot_success = True
            activity_details["sqlglot_success"] = True
            logger.info("SQLGlot parsed successfully. Proceeding to AI extraction.")
        except ParseError as e:
            logger.warning(f"SQLGlot parsing failed: {e}. Using AI-only interpretation.")
            activity_details["sqlglot_error"] = str(e)

        # Build the AI prompt
        try:
            prompt = build_sp_parser_prompt(sql_text)
        except Exception as e:
            error_msg = f"Failed to build prompt for SP parser: {e}"
            logger.error(error_msg)
            agent_logger.log_error("sp_parser_agent", "prompt_building_error", error_msg,
                                 {"procedure_name": procedure_name}, traceback.format_exc())
            raise RuntimeError(error_msg)

        # Call OpenAI API with enhanced error handling
        try:
            system_message = (
                "You are an expert SQL analyst specializing in extracting validation rules "
                "from stored procedures. Focus on identifying all types of validation logic "
                "including constraints, business rules, and data quality checks."
            )

            response = openai_client.call_openai(
                prompt=prompt,
                system_message=system_message,
                agent_name="sp_parser_agent"
            )

            activity_details["output_size"] = len(response)

        except Exception as e:
            error_msg = f"OpenAI API call failed for SP parser: {e}"
            logger.error(error_msg)
            agent_logger.log_error("sp_parser_agent", "api_call_error", error_msg,
                                 {"procedure_name": procedure_name}, traceback.format_exc())
            raise RuntimeError(error_msg)

        # Validate and parse the JSON response
        try:
            validation_result = validate_json_structure(response, "rule_extraction_schema")

            if not validation_result["parsed"]:
                error_msg = f"Failed to parse JSON response: {validation_result['errors']}"
                logger.error(error_msg)
                agent_logger.log_error("sp_parser_agent", "json_parsing_error", error_msg,
                                     {"procedure_name": procedure_name, "response_preview": response[:500]})
                raise ValueError(error_msg)

            if not validation_result["valid"]:
                error_msg = f"Response validation failed: {validation_result['errors']}"
                logger.warning(error_msg)
                # Log as warning but continue - guardrail agent will catch this
                agent_logger.log_error("sp_parser_agent", "schema_validation_warning", error_msg,
                                     {"procedure_name": procedure_name, "validation_errors": validation_result["errors"]})

            # Parse into Pydantic model
            output = RuleExtractionOutput.model_validate_json(response)

            # Additional business logic validation
            _validate_extracted_rules(output, procedure_name, agent_logger)

            # Calculate processing time
            processing_time = time.time() - start_time
            activity_details["processing_time_ms"] = int(processing_time * 1000)

            # Log successful completion
            agent_logger.log_agent_activity(
                "sp_parser_agent",
                "parse_stored_procedure",
                activity_details,
                "success"
            )

            logger.info(f"SP parsing completed successfully for {procedure_name}. "
                       f"Extracted {len(output.rules)} rules in {processing_time:.2f}s")

            return output

        except Exception as e:
            error_msg = f"Failed to validate or parse response: {e}"
            logger.error(error_msg)
            agent_logger.log_error("sp_parser_agent", "response_validation_error", error_msg,
                                 {"procedure_name": procedure_name, "response": response[:1000]},
                                 traceback.format_exc())
            raise ValueError(error_msg)

    except Exception as e:
        # Calculate processing time even for failures
        processing_time = time.time() - start_time
        activity_details["processing_time_ms"] = int(processing_time * 1000)

        # Log failed activity
        agent_logger.log_agent_activity(
            "sp_parser_agent",
            "parse_stored_procedure",
            activity_details,
            "error"
        )

        logger.error(f"SP parsing failed for {procedure_name}: {e}")
        raise

def _validate_extracted_rules(output: RuleExtractionOutput, procedure_name: str,
                             agent_logger) -> None:
    """
    Perform additional business logic validation on extracted rules.

    Args:
        output: The extracted rules output
        procedure_name: Name of the procedure for context
        agent_logger: Logger instance for recording issues
    """
    warnings = []

    # Check for reasonable number of rules
    if len(output.rules) == 0:
        warnings.append("No validation rules extracted - procedure may not contain validation logic")
    elif len(output.rules) > 20:
        warnings.append(f"Large number of rules extracted ({len(output.rules)}) - verify accuracy")

    # Check for rule consistency
    column_rules = {}
    for rule in output.rules:
        for column in rule.columns:
            if column not in column_rules:
                column_rules[column] = []
            column_rules[column].append(rule)

    # Look for potentially contradictory rules
    for column, rules in column_rules.items():
        greater_than_rules = [r for r in rules if r.rule_type == "greater_than"]
        less_than_rules = [r for r in rules if r.rule_type == "less_than"]

        if greater_than_rules and less_than_rules:
            for gt_rule in greater_than_rules:
                for lt_rule in less_than_rules:
                    if (gt_rule.value is not None and lt_rule.value is not None and
                        gt_rule.value >= lt_rule.value):
                        warnings.append(f"Potentially contradictory rules for {column}: "
                                      f">{gt_rule.value} and <{lt_rule.value}")

    # Log warnings if any
    if warnings:
        agent_logger.log_error("sp_parser_agent", "rule_validation_warning",
                             f"Rule validation warnings for {procedure_name}",
                             {"procedure_name": procedure_name, "warnings": warnings})