# AI-Powered Validation Agent System Configuration

# OpenAI Configuration
openai:
  api_key: ${OPENAI_API_KEY}
  model: "gpt-4"
  temperature: 0.0
  max_tokens: 2000
  timeout: 30
  max_retries: 3

# LangChain Configuration
langchain:
  verbose: true
  cache_enabled: true

# Agent Configuration
agents:
  sp_parser:
    enabled: true
    model_override: "gpt-4"
    temperature: 0.1
    max_tokens: 3000

  file_validation:
    enabled: true
    model_override: "gpt-4"
    temperature: 0.0
    batch_size: 100

  data_validation:
    enabled: true
    model_override: "gpt-4"
    temperature: 0.0
    reference_data_timeout: 10

  guardrail:
    enabled: true
    model_override: "gpt-4"
    temperature: 0.0
    strict_mode: true
    auto_block_threshold: 0.8

# File Processing Configuration
file_processing:
  max_file_size_mb: 50
  supported_formats: ["xlsx", "xls", "csv"]
  output_directory: "data/output"
  temp_directory: "data/temp"

# NAV File Specific Configuration
nav_file:
  cleanup_rules:
    strip_special_chars: true
    remove_total_rows: true
    extract_from_parentheses: true
  validation_rules:
    reject_empty_files: true
    check_unique_ids: true
    validate_financial_fields: true

# Database Configuration
database:
  connection_timeout: 30
  query_timeout: 60

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/validation_agent.log"
  max_file_size_mb: 10
  backup_count: 5
  agent_specific_logging: true

# Security and Guardrails
security:
  enable_content_filtering: true
  max_processing_time_minutes: 30
  enable_audit_trail: true

# Streamlit Configuration
streamlit:
  title: "CBRE AI Validation Agent"
  page_icon: "🔍"
  layout: "wide"
  sidebar_state: "expanded"
