import os
import time
import json
import logging
from typing import Optional, Dict, Any, List
from openai import OpenAI
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
import yaml
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class OpenAIClient:
    """
    Modern OpenAI client with LangChain integration, proper error handling,
    and configuration management for AI-powered validation agents.
    """

    def __init__(self, config_path: str = "config/settings.yaml"):
        self.config = self._load_config(config_path)
        self.client = self._initialize_client()
        self.langchain_client = self._initialize_langchain_client()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except UnicodeDecodeError:
            # Try with different encodings if UTF-8 fails
            try:
                with open(config_path, 'r', encoding='utf-8-sig') as file:
                    config = yaml.safe_load(file)
                    return config
            except:
                logger.warning(f"Could not read config file {config_path} due to encoding issues, using defaults")
                return self._get_default_config()
        except Exception as e:
            logger.warning(f"Error loading config file {config_path}: {e}, using defaults")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Return default configuration."""
        return {
            'openai': {
                'model': 'gpt-4',
                'temperature': 0.0,
                'max_tokens': 2000,
                'timeout': 30,
                'max_retries': 3
            }
        }

    def _initialize_client(self) -> OpenAI:
        """Initialize the modern OpenAI client."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")

        return OpenAI(
            api_key=api_key,
            timeout=self.config.get('openai', {}).get('timeout', 30)
        )

    def _initialize_langchain_client(self) -> ChatOpenAI:
        """Initialize LangChain OpenAI client for advanced features."""
        openai_config = self.config.get('openai', {})

        return ChatOpenAI(
            model=openai_config.get('model', 'gpt-4'),
            temperature=openai_config.get('temperature', 0.0),
            max_tokens=openai_config.get('max_tokens', 2000),
            timeout=openai_config.get('timeout', 30),
            max_retries=openai_config.get('max_retries', 3)
        )

    def call_openai(self,
                   prompt: str,
                   model: Optional[str] = None,
                   temperature: Optional[float] = None,
                   max_tokens: Optional[int] = None,
                   system_message: Optional[str] = None,
                   agent_name: Optional[str] = None) -> str:
        """
        Make a call to OpenAI API with modern client and comprehensive error handling.

        Args:
            prompt: The user prompt
            model: Model to use (overrides config)
            temperature: Temperature setting (overrides config)
            max_tokens: Max tokens (overrides config)
            system_message: Optional system message
            agent_name: Name of the calling agent for logging

        Returns:
            The AI response content
        """
        openai_config = self.config.get('openai', {})

        # Use provided parameters or fall back to config
        model = model or openai_config.get('model', 'gpt-4')
        temperature = temperature if temperature is not None else openai_config.get('temperature', 0.0)
        max_tokens = max_tokens or openai_config.get('max_tokens', 2000)
        max_retries = openai_config.get('max_retries', 3)

        # Prepare messages
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})

        # Log the request
        logger.info(f"OpenAI API call from {agent_name or 'unknown'} - Model: {model}")

        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                content = response.choices[0].message.content

                # Log successful response
                logger.info(f"OpenAI API call successful - Tokens used: {response.usage.total_tokens}")

                return content

            except Exception as e:
                logger.warning(f"OpenAI API call attempt {attempt + 1} failed: {str(e)}")

                if attempt < max_retries - 1:
                    # Exponential backoff
                    wait_time = 2 ** attempt
                    time.sleep(wait_time)
                else:
                    logger.error(f"OpenAI API call failed after {max_retries} attempts")
                    raise RuntimeError(f"OpenAI API call failed after {max_retries} retries: {str(e)}")

    def call_with_langchain(self,
                           prompt: str,
                           system_message: Optional[str] = None,
                           agent_name: Optional[str] = None) -> str:
        """
        Make a call using LangChain for advanced features and better integration.

        Args:
            prompt: The user prompt
            system_message: Optional system message
            agent_name: Name of the calling agent for logging

        Returns:
            The AI response content
        """
        logger.info(f"LangChain API call from {agent_name or 'unknown'}")

        try:
            messages = []
            if system_message:
                messages.append(SystemMessage(content=system_message))
            messages.append(HumanMessage(content=prompt))

            response = self.langchain_client.invoke(messages)

            logger.info("LangChain API call successful")
            return response.content

        except Exception as e:
            logger.error(f"LangChain API call failed: {str(e)}")
            raise RuntimeError(f"LangChain API call failed: {str(e)}")

    def validate_json_response(self, response: str, schema: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Validate and parse JSON response from AI.

        Args:
            response: The AI response string
            schema: Optional JSON schema for validation

        Returns:
            Parsed JSON object
        """
        try:
            parsed = json.loads(response)

            # TODO: Add schema validation if schema is provided
            # This would integrate with utils/json_schema_utils.py

            return parsed

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            logger.error(f"Response content: {response[:500]}...")
            raise ValueError(f"Invalid JSON response from AI: {str(e)}")

# Global client instance
_client_instance = None

def get_openai_client() -> OpenAIClient:
    """Get or create the global OpenAI client instance."""
    global _client_instance
    if _client_instance is None:
        _client_instance = OpenAIClient()
    return _client_instance

# Backward compatibility function
def call_openai(prompt: str, model: str = "gpt-4", max_retries: int = 3) -> str:
    """
    Backward compatibility function for existing code.

    Args:
        prompt: The user prompt
        model: Model to use
        max_retries: Maximum number of retries

    Returns:
        The AI response content
    """
    client = get_openai_client()
    return client.call_openai(prompt, model=model, agent_name="legacy_call")
