import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import jsonschema
from jsonschema import ValidationError

logger = logging.getLogger(__name__)

class SchemaValidator:
    """
    Comprehensive JSON schema validation utility for AI validation agents.
    Loads schemas from configuration and provides validation for all agent outputs.
    """

    def __init__(self, schema_config_path: str = "config/schema_definitions.json"):
        self.schemas = self._load_schemas(schema_config_path)

    def _load_schemas(self, config_path: str) -> Dict[str, Any]:
        """Load all schema definitions from configuration file."""
        try:
            with open(config_path, 'r') as file:
                schemas = json.load(file)
                logger.info(f"Loaded {len(schemas)} schema definitions")
                return schemas
        except FileNotFoundError:
            logger.error(f"Schema configuration file not found: {config_path}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in schema configuration: {e}")
            return {}

    def validate_schema(self, data: Dict[str, Any], schema_name: str) -> bool:
        """
        Validate data against a named schema.

        Args:
            data: The data to validate
            schema_name: Name of the schema to use

        Returns:
            True if validation passes

        Raises:
            ValidationError: If validation fails
            ValueError: If schema not found
        """
        if schema_name not in self.schemas:
            raise ValueError(f"Schema '{schema_name}' not found in configuration")

        schema = self.schemas[schema_name]

        try:
            jsonschema.validate(instance=data, schema=schema)
            logger.debug(f"Schema validation passed for {schema_name}")
            return True
        except ValidationError as e:
            logger.error(f"Schema validation failed for {schema_name}: {e.message}")
            raise

    def validate_with_details(self, data: Dict[str, Any], schema_name: str) -> Dict[str, Any]:
        """
        Validate data and return detailed results including errors.

        Args:
            data: The data to validate
            schema_name: Name of the schema to use

        Returns:
            Dictionary with validation results and details
        """
        result = {
            "valid": False,
            "errors": [],
            "warnings": [],
            "schema_used": schema_name
        }

        try:
            self.validate_schema(data, schema_name)
            result["valid"] = True
            logger.info(f"Schema validation successful for {schema_name}")
        except ValidationError as e:
            result["errors"].append({
                "message": e.message,
                "path": list(e.absolute_path),
                "invalid_value": e.instance
            })
            logger.warning(f"Schema validation failed for {schema_name}: {e.message}")
        except ValueError as e:
            result["errors"].append({"message": str(e)})
            logger.error(f"Schema validation error: {e}")

        return result

# Global validator instance
_validator_instance = None

def get_schema_validator() -> SchemaValidator:
    """Get or create the global schema validator instance."""
    global _validator_instance
    if _validator_instance is None:
        _validator_instance = SchemaValidator()
    return _validator_instance

def validate_rules_schema(rules_json: dict) -> bool:
    """
    Validate extracted rules against the rule extraction schema.
    Enhanced version with comprehensive validation.

    Args:
        rules_json: The rules data to validate

    Returns:
        True if validation passes

    Raises:
        ValidationError: If validation fails
    """
    validator = get_schema_validator()
    return validator.validate_schema(rules_json, "rule_extraction_schema")

def validate_validation_result(result_json: dict) -> bool:
    """
    Validate file validation result against schema.

    Args:
        result_json: The validation result to validate

    Returns:
        True if validation passes
    """
    validator = get_schema_validator()
    return validator.validate_schema(result_json, "validation_result_schema")

def validate_business_result(result_json: dict) -> bool:
    """
    Validate business validation result against schema.

    Args:
        result_json: The business validation result to validate

    Returns:
        True if validation passes
    """
    validator = get_schema_validator()
    return validator.validate_schema(result_json, "business_validation_schema")

def validate_guardrail_result(result_json: dict) -> bool:
    """
    Validate guardrail result against schema.

    Args:
        result_json: The guardrail result to validate

    Returns:
        True if validation passes
    """
    validator = get_schema_validator()
    return validator.validate_schema(result_json, "guardrail_result_schema")

def validate_nav_file_row(row_data: dict) -> Dict[str, Any]:
    """
    Validate a single NAV file row against the NAV schema.

    Args:
        row_data: The row data to validate

    Returns:
        Detailed validation results
    """
    validator = get_schema_validator()
    return validator.validate_with_details(row_data, "nav_file_schema")

def validate_json_structure(json_string: str, schema_name: str) -> Dict[str, Any]:
    """
    Parse JSON string and validate against schema.

    Args:
        json_string: JSON string to parse and validate
        schema_name: Name of schema to validate against

    Returns:
        Dictionary with parsing and validation results
    """
    result = {
        "parsed": False,
        "valid": False,
        "data": None,
        "errors": []
    }

    try:
        # Parse JSON
        data = json.loads(json_string)
        result["parsed"] = True
        result["data"] = data

        # Validate against schema
        validator = get_schema_validator()
        validation_result = validator.validate_with_details(data, schema_name)
        result.update(validation_result)

    except json.JSONDecodeError as e:
        result["errors"].append({
            "type": "json_parse_error",
            "message": f"Invalid JSON: {str(e)}",
            "position": e.pos if hasattr(e, 'pos') else None
        })
        logger.error(f"JSON parsing failed: {e}")

    return result
