
from typing import Dict, Any


class SessionContext:
    """
    Lightweight in-memory context store to persist session-level metadata
    across agent calls (can be extended to Redis or DB if needed).
    """
    def __init__(self):
        self._context: Dict[str, Any] = {}

    def set(self, key: str, value: Any):
        self._context[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        return self._context.get(key, default)

    def all(self) -> Dict[str, Any]:
        return self._context

    def reset(self):
        self._context.clear()


# Singleton context to be reused
session_context = SessionContext()
