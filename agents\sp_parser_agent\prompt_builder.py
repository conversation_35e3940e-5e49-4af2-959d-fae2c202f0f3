from pathlib import Path

def build_sp_parser_prompt(sql_text: str) -> str:
    template_path = Path("config/prompt_templates/sp_parser_prompt.txt")
    if not template_path.exists():
        raise FileNotFoundError("Prompt template not found at:", template_path)

    # Try multiple encodings for template file
    encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1252', 'iso-8859-1']

    for encoding in encodings_to_try:
        try:
            template = template_path.read_text(encoding=encoding)
            break
        except UnicodeDecodeError:
            continue
    else:
        # If all fail, use utf-8 with error replacement
        template = template_path.read_text(encoding='utf-8', errors='replace')

    return template.replace("{sql_code}", sql_text)