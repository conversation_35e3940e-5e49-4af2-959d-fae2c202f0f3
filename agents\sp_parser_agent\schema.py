from typing import List, Optional, Union
from pydantic import BaseModel


class Rule(BaseModel):
    columns: List[str]
    rule_type: str
    value: Optional[Union[str, int, float]] = None
    threshold: Optional[float] = None
    reference_table: Optional[str] = None
    logic_snippet: Optional[str] = None
    message: str


class RuleExtractionOutput(BaseModel):
    procedure_name: str
    rules: List[Rule]