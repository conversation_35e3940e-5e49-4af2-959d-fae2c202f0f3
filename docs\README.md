# CBRE AI Validation Agent System

## Overview

The CBRE AI Validation Agent System is a comprehensive, AI-powered data validation platform designed to autonomously validate financial data files against stored procedure rules. The system uses a chain of specialized AI agents, each designed to perform specific validation tasks with intelligent decision-making capabilities.

## 🏗️ Architecture

This system is architected as a **chain of AI-powered modular agents**, where each agent autonomously performs and reasons over one unit of work:

- **SP_Parser_Agent**: Extracts validation rules from SQL stored procedures
- **FileValidationAgent**: Validates individual data rows against extracted rules
- **DataValidationAgent**: Performs sophisticated business logic validation
- **GuardrailAgent**: Enforces safety policies and prevents harmful outputs

Each agent is encapsulated in a reproducible module with clear task scope and well-defined inputs/outputs, enabling easy scaling, swapping, and enhancement.

## 🚀 Key Features

### AI-Powered Intelligence
- **Autonomous Decision Making**: Each agent uses OpenAI's GPT-4 for intelligent reasoning
- **Context-Aware Validation**: Agents consider historical patterns and business context
- **Confidence Scoring**: AI provides confidence levels for validation decisions
- **Adaptive Learning**: System learns from patterns and improves over time

### Comprehensive NAV File Processing
- **Specialized NAV Handling**: Built-in support for Net Asset Value file formats
- **Data Cleanup Rules**: Automatic removal of special characters, TOTAL rows, and parenthetical extraction
- **Financial Data Validation**: Specialized validation for financial fields and business rules
- **Unique ID Management**: Ensures data integrity with unique identifier validation

### Advanced Safety & Guardrails
- **Multi-Layer Security**: Comprehensive security policies and tripwire detection
- **Risk Assessment**: Intelligent risk scoring and escalation procedures
- **Audit Trail**: Complete logging of all agent decisions and reasoning
- **Fail-Safe Operations**: System defaults to safe operations when uncertain

### Enterprise-Grade Logging
- **Agent-Wise Logging**: Detailed logs for each agent with complete error reports
- **Real-Time Monitoring**: Live monitoring of agent activities and performance
- **Comprehensive Metrics**: Processing times, error rates, and confidence scores
- **Audit Compliance**: Full audit trail for regulatory compliance

## 📋 Prerequisites

- Python 3.10 or higher
- OpenAI API key
- Required Python packages (see requirements.txt)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd CBRE_Validation_Agent
   ```

2. **Create and activate virtual environment**:
   ```bash
   python -m venv CBREvenv
   source CBREvenv/bin/activate  # On Windows: CBREvenv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   ```

5. **Configure the system**:
   - Edit `config/settings.yaml` for system configuration
   - Review `docs/guardrail_policies.md` for safety policies

## 🎯 Usage

### Command Line Interface

**Basic Usage**:
```bash
python main.py procedure.sql data.xlsx
```

**Advanced Usage**:
```bash
python main.py --sp procedure.sql --data data.xlsx --nav-processing --batch-size 200 --output results.json
```

**Available Options**:
- `--nav-processing`: Enable NAV-specific file processing (default: enabled)
- `--batch-size`: Set batch size for large files (default: 100)
- `--output`: Save results to JSON file
- `--verbose`: Enable detailed logging
- `--quiet`: Suppress non-error output

### Streamlit Web Interface

Launch the interactive web interface:
```bash
streamlit run streamlit_app/app.py
```

Features:
- **File Upload**: Drag-and-drop interface for SQL and data files
- **Real-Time Processing**: Live progress tracking and logging
- **Results Dashboard**: Interactive charts and detailed analysis
- **Agent Logs**: Real-time monitoring of agent activities
- **System Status**: Health monitoring and configuration

### Programmatic Usage

```python
from agent_chain import run_validation_chain

# Run validation with default settings
results = run_validation_chain("procedure.sql", "data.xlsx")

# Run with custom configuration
results = run_validation_chain(
    sql_file_path="procedure.sql",
    excel_path="data.xlsx",
    enable_nav_processing=True,
    batch_size=200
)

print(f"Status: {results['status']}")
print(f"Violations: {len(results['violations'])}")
```

## 🔧 Configuration

### System Configuration (`config/settings.yaml`)

```yaml
# OpenAI Configuration
openai:
  api_key: ${OPENAI_API_KEY}
  model: "gpt-4"
  temperature: 0.0
  max_tokens: 2000

# Agent Configuration
agents:
  sp_parser:
    enabled: true
    temperature: 0.1
  file_validation:
    enabled: true
    batch_size: 100
  data_validation:
    enabled: true
  guardrail:
    enabled: true
    strict_mode: true

# NAV File Configuration
nav_file:
  cleanup_rules:
    strip_special_chars: true
    remove_total_rows: true
    extract_from_parentheses: true
  validation_rules:
    reject_empty_files: true
    check_unique_ids: true
    validate_financial_fields: true
```

### Schema Definitions (`config/schema_definitions.json`)

Defines JSON schemas for:
- Rule extraction output
- Validation results
- Business validation results
- Guardrail results
- NAV file schema

## 🛡️ Safety & Guardrails

The system implements comprehensive safety policies:

### Core Safety Principles
1. **Data Privacy**: Never expose sensitive financial data in logs
2. **Output Integrity**: All AI outputs are verifiable and traceable
3. **Autonomous Boundaries**: AI cannot make irreversible decisions without oversight

### Specific Guardrail Policies
- **GP-001**: Data volume limits (files >50MB require review)
- **GP-002**: Validation rule consistency checks
- **GP-003**: Financial value anomaly detection
- **GP-004**: Excessive error rate prevention
- **GP-005**: SQL injection prevention
- **GP-006**: API rate limiting
- **GP-007**: Output format validation
- **GP-008**: Temporal consistency validation
- **GP-009**: Reference data integrity checks
- **GP-010**: AI confidence thresholds

## 📊 Monitoring & Logging

### Log Files
- `logs/validation_agent.log`: Main system log
- `logs/sp_parser_agent.log`: SP parser specific logs
- `logs/file_validation_agent.log`: File validation logs
- `logs/data_validation_agent.log`: Business validation logs
- `logs/guardrail_agent.log`: Guardrail enforcement logs
- `logs/errors.log`: Error-only log
- `logs/guardrail_violations.json`: Detailed violation records
- `logs/agent_activity.json`: Agent activity tracking
- `logs/audit_trail.json`: Compliance audit trail

### Metrics Tracked
- Processing times per agent
- Error rates and confidence scores
- Violation counts by severity and policy
- Resource usage and performance metrics
- Business validation outcomes

## 🔍 NAV File Processing

### Cleanup Rules
1. **Strip Special Characters**: Remove non-essential special characters
2. **Remove TOTAL Rows**: Automatically detect and remove summary rows
3. **Extract from Parentheses**: Extract meaningful values from parenthetical expressions

### Validation Rules
1. **Empty File Rejection**: Reject files with no data after cleanup
2. **Unique ID Validation**: Ensure UNIQUE_ID field is present and unique
3. **Financial Field Validation**: Validate that financial fields are not all null

### Supported NAV Schema
- `UNIQUE_ID`: Unique identifier (required)
- `FUND_ID`: Fund identifier (required)
- `NAV`: Net Asset Value (required)
- `PREVIOUS_NAV`: Previous period NAV
- `FUND_CODE`: Fund code for reference lookup
- `VALUATION_DATE`: Date of valuation
- `CURRENCY`: Currency code

## 🚨 Error Handling

The system provides comprehensive error handling:

### Error Types
- **System Errors**: API failures, configuration issues
- **Validation Errors**: Data quality problems, rule violations
- **Business Errors**: Logic inconsistencies, threshold breaches
- **Security Errors**: Potential threats, policy violations

### Error Recovery
- Automatic retry with exponential backoff
- Graceful degradation for non-critical failures
- Fail-safe defaults for uncertain situations
- Complete error context preservation

## 📈 Performance

### Optimization Features
- **Batch Processing**: Efficient handling of large datasets
- **Parallel Processing**: Concurrent validation where possible
- **Caching**: Intelligent caching of repeated operations
- **Resource Management**: Memory and API usage optimization

### Scalability
- Modular agent architecture enables horizontal scaling
- Configurable batch sizes for different workloads
- Database integration ready for enterprise deployment
- Cloud deployment compatible

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

[License information to be added]

## 🆘 Support

For support and questions:
- Check the logs in the `logs/` directory
- Review the configuration in `config/`
- Consult the guardrail policies in `docs/guardrail_policies.md`
- Use the Streamlit interface for interactive debugging

## 🔄 Version History

- **v1.0.0**: Initial release with full AI agent chain
- Comprehensive NAV file processing
- Advanced guardrail system
- Enterprise logging and monitoring
- Streamlit web interface