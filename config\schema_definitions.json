{"rule_extraction_schema": {"type": "object", "properties": {"procedure_name": {"type": "string", "description": "Name of the stored procedure"}, "rules": {"type": "array", "items": {"type": "object", "properties": {"columns": {"type": "array", "items": {"type": "string"}, "description": "List of columns this rule applies to"}, "rule_type": {"type": "string", "enum": ["not_null", "greater_than", "less_than", "equals", "not_equals", "in_range", "regex_match", "exists_in_reference", "percentage_change_limit", "custom_sql_logic", "conditional_validation", "unique_constraint", "format_validation"], "description": "Type of validation rule"}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "null"}], "description": "Expected value for comparison rules"}, "threshold": {"type": "number", "description": "Threshold value for percentage or range checks"}, "reference_table": {"type": "string", "description": "Reference table name for lookup validations"}, "logic_snippet": {"type": "string", "description": "Custom SQL logic for complex validations"}, "message": {"type": "string", "description": "Error message to display when rule fails"}}, "required": ["rule_type", "message"], "additionalProperties": false}}}, "required": ["procedure_name", "rules"], "additionalProperties": false}, "validation_result_schema": {"type": "object", "properties": {"error": {"type": "boolean", "description": "Whether validation failed"}, "error_message": {"type": "string", "description": "Detailed error message if validation failed"}, "warnings": {"type": "array", "items": {"type": "string"}, "description": "Non-critical warnings"}, "validated_fields": {"type": "array", "items": {"type": "string"}, "description": "List of fields that were validated"}}, "required": ["error", "error_message"], "additionalProperties": false}, "business_validation_schema": {"type": "object", "properties": {"valid": {"type": "boolean", "description": "Whether business logic validation passed"}, "reason": {"type": "string", "description": "Explanation of validation result"}, "override_allowed": {"type": "boolean", "description": "Whether this validation can be overridden by user"}, "confidence_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "AI confidence in the validation decision"}, "recommendations": {"type": "array", "items": {"type": "string"}, "description": "Suggested actions or corrections"}}, "required": ["valid", "reason", "override_allowed"], "additionalProperties": false}, "guardrail_result_schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["approved", "blocked", "review_required"], "description": "Final guardrail decision"}, "violations": {"type": "array", "items": {"type": "object", "properties": {"policy": {"type": "string"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "description": {"type": "string"}, "recommendation": {"type": "string"}}, "required": ["policy", "severity", "description"]}, "description": "List of policy violations detected"}, "action": {"type": "string", "enum": ["proceed", "review_required", "block_processing", "request_approval"], "description": "Recommended action based on guardrail analysis"}, "risk_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Overall risk assessment score"}}, "required": ["status", "violations", "action"], "additionalProperties": false}, "nav_file_schema": {"type": "object", "properties": {"UNIQUE_ID": {"type": "string", "description": "Unique identifier for each record"}, "FUND_ID": {"type": "string", "description": "Fund identifier"}, "NAV": {"type": "number", "description": "Net Asset Value"}, "PREVIOUS_NAV": {"type": "number", "description": "Previous period NAV for comparison"}, "FUND_CODE": {"type": "string", "description": "Fund code for reference table lookup"}, "VALUATION_DATE": {"type": "string", "format": "date", "description": "Date of valuation"}, "CURRENCY": {"type": "string", "description": "Currency code"}}, "required": ["UNIQUE_ID", "FUND_ID", "NAV"], "additionalProperties": true}}